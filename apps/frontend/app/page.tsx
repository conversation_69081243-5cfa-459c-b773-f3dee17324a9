/**
 * 主应用页面 - 重构版本
 * 🎯 核心价值：数据驱动的矩阵应用，最小化架构，零冗余
 * 📦 功能范围：矩阵渲染、控制面板、模式切换
 * 🔄 架构设计：基于新的核心架构，完全数据驱动的视图
 */

'use client';

import Controls from '@/components/Controls';
import Matrix from '@/components/Matrix';
import Button from '@/components/ui/Button';
import { CloseIcon, MenuIcon } from '@/components/ui/Icons';
import { useResponsiveControls } from '@/hooks/useResponsiveControls';
import { useCallback, useEffect, useState } from 'react';

import type { BusinessMode, Coordinate } from '@/core/matrix/MatrixTypes';

// ===== 主应用组件 =====

export default function HomePage() {
  // 响应式控制面板逻辑
  const {
    controlsVisible,
    setControlsVisible,
    toggleControls,
    isDesktop
  } = useResponsiveControls();

  const [isClient, setIsClient] = useState(false);

  // 确保客户端渲染一致性，避免 hydration 错误
  useEffect(() => {
    setIsClient(true);
  }, []);

  // 事件处理 - 简化版本
  const handleCellClick = useCallback((coordinate: Coordinate) => {
    // 简化的点击处理
    console.log('Cell clicked:', coordinate);
  }, []);

  const handleCellDoubleClick = useCallback((coordinate: Coordinate) => {
    // 简化的双击处理
    console.log('Cell double-clicked:', coordinate);
  }, []);

  const handleModeChange = useCallback((mode: BusinessMode) => {
    // 简化的模式切换处理
    console.log('Mode changed to:', mode);
  }, []);

  const handleReset = useCallback(() => {
    // 简化的重置处理
    console.log('Matrix reset');
  }, []);




  // 在客户端渲染完成前显示加载状态，避免 hydration 错误
  if (!isClient) {
    return (
      <div className="app-container h-screen flex bg-gray-100 items-center justify-center">
        <div className="text-gray-600">加载中...</div>
      </div>
    );
  }

  return (
    <div className="app-container min-h-screen flex">
      {/* 主矩阵区域 - 响应式显示，保持1:1比例 */}
      <div className="matrix-area flex-1 flex items-center justify-center p-4">
        {/* 矩阵容器 - 响应式尺寸，保持1:1比例 */}
        <div className="matrix-container w-full h-full max-w-full max-h-full flex items-center justify-center">
          <Matrix
            onCellClick={handleCellClick}
            onCellDoubleClick={handleCellDoubleClick}
            onModeChange={handleModeChange}
            className=""
            style={{
              width: '100%',
              height: '100%',
              maxWidth: 'min(100vw - 1.5rem, 100vh - 1.5rem, 1122px)',
              maxHeight: 'min(100vw - 1.5rem, 100vh - 1.5rem, 1122px)',
              // 移除minHeight强制设置，让Matrix组件自己控制尺寸
              // 这样可以避免在小窗口中出现视觉切边问题
            }}
          />
        </div>
      </div>

      {/* 控制面板 - 响应式显示 */}
      {controlsVisible && isDesktop && (
        <div className="controls-sidebar flex w-80 bg-white border-l border-gray-200 flex-shrink-0 flex-col">
          <div className="p-4 border-b border-gray-200">
            <div className="flex items-center justify-between">
              <h2 className="text-lg font-semibold text-gray-800">矩阵系统</h2>
              <Button
                variant="icon"
                size="cell"
                onClick={() => setControlsVisible(false)}
                title="隐藏控制面板"
              >
                <CloseIcon size={14} />
              </Button>
            </div>
          </div>

          <div className="p-4">
            <Controls
              onModeChange={handleModeChange}
              onReset={handleReset}
            />
          </div>
        </div>
      )}



      {/* 菜单图标按钮（当面板隐藏时） */}
      {!controlsVisible && (
        <div className="fixed top-4 right-4 z-10">
          <Button
            variant="icon"
            size="cell"
            onClick={toggleControls}
            title="显示控制面板"
          >
            <MenuIcon size={18} />
          </Button>
        </div>
      )}

      {/* 应用专用样式 */}
      <style jsx global>{`
        .app-container:focus {
          outline: none;
        }

        /* 极简化设计 - 去除不必要的视觉分割 */
        .matrix-area {
          background: transparent;
        }

        /* 确保矩阵响应式显示 */
        .matrix-container {
          display: flex;
          align-items: center;
          justify-content: center;
        }

        /* 确保矩阵在大屏幕下完整显示 */
        @media (min-width: 1200px) and (min-height: 1200px) {
          .matrix-area {
            padding: 1rem;
          }
        }
      `}</style>
    </div>
  );
}