/**
 * 响应式控制面板Hook
 * 🎯 核心价值：智能的响应式控制面板显示逻辑
 * 📦 功能范围：窗口尺寸监听、自动显示/隐藏控制面板
 * 🔄 架构设计：基于窗口尺寸的自动化UI控制
 */

'use client';

import { useCallback, useEffect, useState } from 'react';

// ===== 配置常量 =====

/** 响应式配置 */
const RESPONSIVE_CONFIG = {
  /** 矩阵最大尺寸 */
  MATRIX_MAX_SIZE: 1122, // 33 * 34px
  /** 控制面板宽度 */
  CONTROLS_WIDTH: 320, // w-80 = 320px
  /** 最小边距 */
  MIN_MARGIN: 32, // 左右各16px边距
  /** 自动隐藏临界点 */
  get AUTO_HIDE_BREAKPOINT() {
    return this.MATRIX_MAX_SIZE + this.CONTROLS_WIDTH + this.MIN_MARGIN;
  }
} as const;

// ===== Hook接口 =====

interface UseResponsiveControlsReturn {
  /** 当前窗口宽度 */
  windowWidth: number;
  /** 当前窗口高度 */
  windowHeight: number;
  /** 是否应该自动隐藏控制面板 */
  shouldAutoHide: boolean;
  /** 是否为移动设备 */
  isMobile: boolean;
  /** 是否为平板设备 */
  isTablet: boolean;
  /** 是否为桌面设备 */
  isDesktop: boolean;
  /** 控制面板是否可见 */
  controlsVisible: boolean;
  /** 设置控制面板可见性 */
  setControlsVisible: (visible: boolean) => void;
  /** 切换控制面板可见性 */
  toggleControls: () => void;
}

// ===== 主Hook =====

export const useResponsiveControls = (): UseResponsiveControlsReturn => {
  // 状态管理
  const [windowWidth, setWindowWidth] = useState(0);
  const [windowHeight, setWindowHeight] = useState(0);
  const [controlsVisible, setControlsVisible] = useState(true);
  const [isClient, setIsClient] = useState(false);

  // 确保客户端渲染
  useEffect(() => {
    setIsClient(true);
  }, []);

  // 窗口尺寸监听
  useEffect(() => {
    if (!isClient) return;

    const handleResize = () => {
      const width = window.innerWidth;
      const height = window.innerHeight;
      
      setWindowWidth(width);
      setWindowHeight(height);
    };

    // 初始化
    handleResize();

    // 添加监听器
    window.addEventListener('resize', handleResize);
    
    // 清理
    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, [isClient]);

  // 计算响应式状态
  const shouldAutoHide = windowWidth > 0 && windowWidth < RESPONSIVE_CONFIG.AUTO_HIDE_BREAKPOINT;
  const isMobile = windowWidth > 0 && windowWidth < 768;
  const isTablet = windowWidth >= 768 && windowWidth < 1024;
  const isDesktop = windowWidth >= 1024;

  // 自动隐藏逻辑
  useEffect(() => {
    if (shouldAutoHide && controlsVisible) {
      setControlsVisible(false);
    }
  }, [shouldAutoHide, controlsVisible]);

  // 切换控制面板可见性
  const toggleControls = useCallback(() => {
    setControlsVisible(prev => !prev);
  }, []);

  return {
    windowWidth,
    windowHeight,
    shouldAutoHide,
    isMobile,
    isTablet,
    isDesktop,
    controlsVisible,
    setControlsVisible,
    toggleControls,
  };
};

// ===== 导出配置 =====

export { RESPONSIVE_CONFIG };

// ===== 类型导出 =====

export type { UseResponsiveControlsReturn };
